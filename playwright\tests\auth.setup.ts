import { test as setup, expect } from "@playwright/test";
import path from "path";

const authFile = path.join(__dirname, "../.auth/user.json");

setup("authenticate", async ({ page }) => {
  await page.goto("/");

  await page.locator("form button").click();

  await page.getByPlaceholder("Email, phone, or Skype").fill(process.env.PLAYWRIGHT_USERNAME!);
  await page.getByRole("button", { name: "Next" }).click();

  await page.getByPlaceholder("Password").click();
  await page.getByPlaceholder("Password").fill(process.env.PLAYWRIGHT_PASSWORD!);

  await page.getByRole("button", { name: "Sign in" }).click();
  await page.getByLabel("Don't show this again").check();
  await page.getByRole("button", { name: "Yes" }).click();

  const logoutButton = page.getByRole("button", { name: "Logout" });
  await expect(logoutButton).toBeVisible();

  await page.context().storageState({ path: authFile });
});
