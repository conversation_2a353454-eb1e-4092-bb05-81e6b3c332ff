import { test, expect } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";
import { TestDataUtils } from "../../utils/testDataUtils";

test.describe("Search Projects tests", () => {
  let pom: PomManager;
  const testDataUtils = new TestDataUtils();
  let testProject: Record<string, string>;

  test.beforeAll(async () => {
    testProject = await testDataUtils.addUniqueProject();
  });

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.projectsPage.goToProjectsPage();
    await pom.projectsPage.searchInput.fill("");
  });

  test.afterAll(async () => {
    await testDataUtils.deleteUniqueProjects(testProject.projectTitle);
    await testDataUtils.disconnect();
  });

  const testCases = [
    "email",
    "number",
    "projectTitle",
    "location",
    "agency",
    "contactFirstName",
    "contactLastName",
    "platform",
    "projectLink",
    "comment",
    "rateOnsite",
    "role",
  ];

  testCases.forEach((testCase) => {
    test(`should be able to search for projects by ${testCase}`, async () => {
      await pom.projectsPage.searchInput.fill(testProject[testCase]);
      expect(await pom.projectsPage.projectCount()).toBe(1);
    });
  });

  testCases.forEach((testCase) => {
    test(`should be able to partial search for projects by ${testCase}`, async () => {
      await pom.projectsPage.searchInput.fill(testProject[testCase].slice(0, 10));
      expect(await pom.projectsPage.projectCount()).toBe(1);
    });
  });
});
