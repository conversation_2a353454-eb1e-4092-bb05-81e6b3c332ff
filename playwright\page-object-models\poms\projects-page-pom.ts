import { type Locator, type Page } from "@playwright/test";

export class ProjectsPagePom {
  readonly page: Page;
  readonly addIcon: Locator;
  readonly agency: Locator;
  readonly agencyFilterAsc: Locator;
  readonly agencyFilterDesc: Locator;
  readonly cancelConfirmButton: Locator;
  readonly commentDetails: Locator;
  readonly confirmButton: Locator;
  readonly confirmDialog: Locator;
  readonly contactDetails: Locator;
  readonly contactEmail: Locator;
  readonly contactFirstName: Locator;
  readonly contactLastName: Locator;
  readonly contactNumber: Locator;
  readonly createdByUser: Locator;
  readonly createdForUser: Locator;
  readonly dateOfApplication: Locator;
  readonly dateOfProjectStart: Locator;
  readonly editIcon: Locator;
  readonly firstProject: Locator;
  readonly idSortAsc: Locator;
  readonly idSortDesc: Locator;
  readonly infoBadgeGroup: Locator;
  readonly infoHeader: Locator;
  readonly jobReference: Locator;
  readonly lastProject: Locator;
  readonly location: Locator;
  readonly desktopLogoutButton: Locator;
  readonly mobileLogoutButton: Locator;
  readonly offerDetails: Locator;
  readonly platform: Locator;
  readonly projectComment: Locator;
  readonly projectDetails: Locator;
  readonly projectForm: Locator;
  readonly projectId: Locator;
  readonly projectItem: Locator;
  readonly projectLink: Locator;
  readonly projectRole: Locator;
  readonly projectStatus: Locator;
  readonly projectTitle: Locator;
  readonly projectsListContainer: Locator;
  readonly rateOffsite: Locator;
  readonly rateOnsite: Locator;
  readonly remotePercentage: Locator;
  readonly sortButtons: Record<string, { asc: Locator; desc: Locator }>;
  readonly searchInput: Locator;
  readonly sortContactNameAsc: Locator;
  readonly sortContactNameDesc: Locator;
  readonly sortCreatedByUserAsc: Locator;
  readonly sortCreatedByUserDesc: Locator;
  readonly sortCreatedForUserAsc: Locator;
  readonly sortCreatedForUserDesc: Locator;
  readonly sortDateOfApplicationAsc: Locator;
  readonly sortDateOfApplicationDesc: Locator;
  readonly sortDropdown: Locator;
  readonly sortIcon: Locator;
  readonly sortProjectTitleAsc: Locator;
  readonly sortProjectTitleDesc: Locator;
  readonly sortRateAsc: Locator;
  readonly sortRateDesc: Locator;
  readonly toastNotification: Locator;
  readonly trashIcon: Locator;
  readonly utilization: Locator;

  constructor(page: Page) {
    this.page = page;

    this.addIcon = page.getByTestId("addIcon");
    this.agency = page.getByTestId("agency");
    this.agencyFilterAsc = page.getByTestId("sort-asc-agency");
    this.agencyFilterDesc = page.getByTestId("sort-desc-agency");
    this.cancelConfirmButton = page.getByTestId("cancelConfirmButton");
    this.commentDetails = page.getByTestId("commentDetails");
    this.confirmButton = page.getByTestId("confirmButton");
    this.confirmDialog = page.getByTestId("confirmDialog");
    this.contactDetails = page.getByTestId("contactDetails");
    this.contactEmail = page.getByTestId("contactEmail");
    this.contactFirstName = page.getByTestId("contactFirstName");
    this.contactLastName = page.getByTestId("contactLastName");
    this.contactNumber = page.getByTestId("contactNumber");
    this.createdByUser = page.getByTestId("createdByUser");
    this.createdForUser = page.getByTestId("createdForUser");
    this.dateOfApplication = page.getByTestId("dateOfApplication");
    this.dateOfProjectStart = page.getByTestId("dateOfProjectStart");
    this.editIcon = page.getByTestId("editIcon");
    this.idSortAsc = page.getByTestId("sort-asc-id");
    this.idSortDesc = page.getByTestId("sort-desc-id");
    this.infoBadgeGroup = page.getByTestId("infoBadgeGroup");
    this.infoHeader = page.getByTestId("infoHeader");
    this.jobReference = page.getByTestId("jobReference");
    this.location = page.getByTestId("projectLocation");
    this.desktopLogoutButton = page.getByTestId("desktopLogoutButton");
    this.mobileLogoutButton = page.getByTestId("mobileLogoutButton");
    this.offerDetails = page.getByTestId("offerDetails");
    this.platform = page.getByTestId("platform");
    this.projectComment = page.getByTestId("projectComment");
    this.projectDetails = page.getByTestId("projectDetails");
    this.projectForm = page.getByTestId("projectForm");
    this.projectId = page.getByTestId("projectId");
    this.projectItem = page.getByTestId("projectItem");
    this.firstProject = this.projectItem.first();
    this.lastProject = this.projectItem.last();
    this.projectLink = page.getByTestId("projectLink");
    this.projectRole = page.getByTestId("projectRole");
    this.projectStatus = page.getByTestId("projectStatus");
    this.projectTitle = page.getByTestId("projectTitle");
    this.projectsListContainer = page.getByTestId("projectsListContainer");
    this.rateOffsite = page.getByTestId("rateOffsite");
    this.rateOnsite = page.getByTestId("rateOnsite");
    this.remotePercentage = page.getByTestId("remotePercentage");
    this.searchInput = page.getByTestId("searchInput");
    this.sortContactNameAsc = page.getByTestId("sort-asc-contactFirstName");
    this.sortContactNameDesc = page.getByTestId("sort-desc-contactFirstName");
    this.sortCreatedByUserAsc = page.getByTestId("sort-asc-createdByUser");
    this.sortCreatedByUserDesc = page.getByTestId("sort-desc-createdByUser");
    this.sortCreatedForUserAsc = page.getByTestId("sort-asc-createdForUser");
    this.sortCreatedForUserDesc = page.getByTestId("sort-desc-createdForUser");
    this.sortDateOfApplicationAsc = page.getByTestId("sort-asc-dateOfApplication");
    this.sortDateOfApplicationDesc = page.getByTestId("sort-desc-dateOfApplication");
    this.sortDropdown = page.getByTestId("sortDropdown");
    this.sortIcon = page.getByTestId("sortIcon");
    this.sortProjectTitleAsc = page.getByTestId("sort-asc-projectTitle");
    this.sortProjectTitleDesc = page.getByTestId("sort-desc-projectTitle");
    this.sortRateAsc = page.getByTestId("sort-asc-rateOnsite");
    this.sortRateDesc = page.getByTestId("sort-desc-rateOnsite");
    this.toastNotification = page.getByTestId("toastNotification");
    this.trashIcon = page.getByTestId("trashIcon");
    this.utilization = page.getByTestId("utilization");
    this.sortButtons = {
      projectId: {
        asc: this.idSortAsc,
        desc: this.idSortDesc,
      },
      projectTitle: {
        asc: this.sortProjectTitleAsc,
        desc: this.sortProjectTitleDesc,
      },
      agency: {
        asc: this.agencyFilterAsc,
        desc: this.agencyFilterDesc,
      },
      contactFirstName: {
        asc: this.sortContactNameAsc,
        desc: this.sortContactNameDesc,
      },
      rateOnsite: {
        asc: this.sortRateAsc,
        desc: this.sortRateDesc,
      },
      dateOfApplication: {
        asc: this.sortDateOfApplicationAsc,
        desc: this.sortDateOfApplicationDesc,
      },
      createdByUser: {
        asc: this.sortCreatedByUserAsc,
        desc: this.sortCreatedByUserDesc,
      },
      createdForUser: {
        asc: this.sortCreatedForUserAsc,
        desc: this.sortCreatedForUserDesc,
      },
    };
  }

  async goToProjectsPage() {
    await this.page.goto("/");
    await this.projectsListContainer.waitFor({ state: "visible" });
  }

  async getFirstProject(): Promise<Record<string, string>> {
    const firstProject = this.projectItem.first();

    const fieldLocators: Record<string, Locator> = {
      projectLink: this.projectLink,
      jobReference: this.jobReference,
      projectTitle: this.projectTitle,
      location: this.location,
      agency: this.agency,
      platform: this.platform,
      contactEmail: this.contactEmail,
      contactNumber: this.contactNumber,
      contactFirstName: this.contactFirstName,
      contactLastName: this.contactLastName,
      rateOffsite: this.rateOffsite,
      rateOnsite: this.rateOnsite,
      dateOfApplication: this.dateOfApplication,
      createdByUser: this.createdByUser,
      createdForUser: this.createdForUser,
      projectStatus: this.projectStatus,
      remotePercentage: this.remotePercentage,
      dateOfProjectStart: this.dateOfProjectStart,
      projectRole: this.projectRole,
      utilization: this.utilization,
      projectComment: this.projectComment,
    };

    const projectData: Record<string, string> = {};
    for (const [fieldName, locator] of Object.entries(fieldLocators)) {
      try {
        if (fieldName === "projectLink") {
          const href = await firstProject.locator(locator).getAttribute("href");
          projectData[fieldName] = href || "";
        } else {
          const value = await firstProject.locator(locator).textContent();
          projectData[fieldName] = value || "";
        }
      } catch (error: any) {
        projectData[fieldName] = "";
        console.warn(`Failed to extract ${fieldName} from first project: ${error.message}`);
      }
    }

    return projectData;
  }

  async projectCount(): Promise<number> {
    return await this.projectItem.count();
  }

  async openProjectForm() {
    await this.addIcon.click();
    await this.projectForm.waitFor({ state: "visible" });
  }

  async getFormValueFromProject(project: Locator, formValue: Locator): Promise<string> {
    return (await project.locator(formValue).textContent()) || "";
  }
}
