"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut, useSession } from "next-auth/react";

export const Navbar: React.FC = () => {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className="bg-white border-b border-gray-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Left side - Logo and Desktop Navigation */}
          <div className="flex items-center">
            <Image className="h-8 w-auto" src="/tme-logo-black-blue.svg" alt="Logo" width={288} height={81} data-testid="tmeLogo" />
            <span className="ml-3 text-xl font-semibold">ProjectScout</span>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center ml-6">
              <span className="text-gray-300">|</span>
              <div className="flex items-center space-x-6 ml-6">
                <Link
                  href="/"
                  className={`font-medium transition-colors duration-200 ${pathname === "/" ? "text-blue-600" : "text-gray-700 hover:text-blue-600"}`}
                >
                  Home
                </Link>
                <Link
                  href="/myProjects"
                  className={`font-medium transition-colors duration-200 ${
                    pathname === "/myProjects" ? "text-blue-600" : "text-gray-700 hover:text-blue-600"
                  }`}
                >
                  My Projects
                </Link>
              </div>
            </div>
          </div>

          {/* Right side - User info and Desktop Logout */}
          <div className="hidden md:flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Image src={session?.user?.image || ""} alt="User Avatar" width={36} height={36} className="rounded-full border-2 border-gray-200" />
              <span className="text-sm font-medium text-gray-800">{session?.user?.name}</span>
            </div>
            <button
              type="button"
              onClick={() => signOut()}
              className="px-3 py-1 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors duration-200"
              data-testid="desktopLogoutButton"
            >
              Logout
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              type="button"
              onClick={toggleMobileMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors duration-200"
              aria-expanded={isMobileMenuOpen}
              aria-label="Toggle navigation menu"
            >
              <span className="sr-only">Open main menu</span>
              {/* Hamburger icon */}
              <svg
                className={`${isMobileMenuOpen ? "hidden" : "block"} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              {/* Close icon */}
              <svg
                className={`${isMobileMenuOpen ? "block" : "hidden"} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        <div className={`md:hidden ${isMobileMenuOpen ? "block" : "hidden"}`}>
          <div className="px-2 pt-2 pb-3 space-y-1 border-t border-gray-200 bg-white">
            {/* Mobile Navigation Links */}
            <Link
              href="/"
              onClick={closeMobileMenu}
              className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                pathname === "/" ? "text-blue-600 bg-blue-50" : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
              }`}
            >
              Home
            </Link>
            <Link
              href="/myProjects"
              onClick={closeMobileMenu}
              className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                pathname === "/myProjects" ? "text-blue-600 bg-blue-50" : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
              }`}
            >
              My Projects
            </Link>

            {/* Mobile User Info and Logout */}
            <div className="pt-4 pb-3 border-t border-gray-200">
              <div className="flex items-center px-3 mb-3">
                <Image src={session?.user?.image || ""} alt="User Avatar" width={32} height={32} className="rounded-full border-2 border-gray-200" />
                <span className="ml-3 text-sm font-medium text-gray-800">{session?.user?.name}</span>
              </div>
              <button
                type="button"
                onClick={() => {
                  closeMobileMenu();
                  signOut();
                }}
                className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-white bg-blue-500 hover:bg-blue-600 transition-colors duration-200"
                data-testid="mobileLogoutButton"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};
