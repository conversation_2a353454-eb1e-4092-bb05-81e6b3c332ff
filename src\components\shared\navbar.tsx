"use client";

// import { signOut } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut, useSession } from "next-auth/react";

export const Navbar: React.FC = () => {
  const { data: session } = useSession();
  const pathname = usePathname();

  return (
    <nav className="bg-white border-b border-gray-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Image className="h-8 w-auto" src="/tme-logo-black-blue.svg" alt="Logo" width={288} height={81} data-testid="tmeLogo" />
            <span className="ml-3 text-xl font-semibold">ProjectScout</span>

            <span className="ml-6">|</span>

            <div className="flex items-center space-x-6 ml-6">
              <Link
                href="/"
                className={`font-medium transition-colors duration-200 ${pathname === "/" ? "text-blue-600" : "text-gray-700 hover:text-blue-600"}`}
              >
                Home
              </Link>

              <Link
                href="/myProjects"
                className={`font-medium transition-colors duration-200 ${
                  pathname === "/myProjects" ? "text-blue-600" : "text-gray-700 hover:text-blue-600"
                }`}
              >
                My Projects
              </Link>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Image src={session?.user?.image || ""} alt="User Avatar" width={36} height={36} className="rounded-full border-2 border-gray-200" />
              <span className="text-sm font-medium text-gray-800">{session?.user?.name}</span>
            </div>

            <button
              type="button"
              onClick={() => signOut()}
              className="px-3 py-1 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors duration-200"
              data-testid="logoutButton"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};
