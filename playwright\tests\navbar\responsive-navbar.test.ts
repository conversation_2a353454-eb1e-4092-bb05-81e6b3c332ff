import { test, expect } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";

test.describe("Responsive Navbar Tests", () => {
  let pom: PomManager;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.projectsPage.goToProjectsPage();
  });

  test.describe("Desktop View (1280px and above)", () => {
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize({ width: 1280, height: 720 });
    });

    test("should display full navbar with all elements visible", async ({ page }) => {
      // Logo and brand should be visible
      const logo = page.getByTestId("tmeLogo");
      await expect(logo).toBeVisible();

      const brandText = page.locator("text=ProjectScout");
      await expect(brandText).toBeVisible();

      // Desktop navigation links should be visible
      const homeLink = page.locator("nav a[href='/']").first();
      const myProjectsLink = page.locator("nav a[href='/myProjects']").first();
      await expect(homeLink).toBeVisible();
      await expect(myProjectsLink).toBeVisible();

      // User info and desktop logout button should be visible
      await expect(pom.projectsPage.desktopLogoutButton).toBeVisible();

      // Mobile menu button should be hidden
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await expect(mobileMenuButton).toBeHidden();

      // Mobile menu should be hidden
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();
      await expect(mobileMenu).toBeHidden();
    });

    test("should highlight active navigation link", async ({ page }) => {
      // Home should be active on home page
      const homeLink = page.locator("nav a[href='/']").first();
      await expect(homeLink).toHaveClass(/text-blue-600/);

      // Navigate to My Projects
      await page.click("nav a[href='/myProjects']");
      await page.waitForURL("/myProjects");

      // My Projects should now be active
      const myProjectsLink = page.locator("nav a[href='/myProjects']").first();
      await expect(myProjectsLink).toHaveClass(/text-blue-600/);
    });

    test("should allow logout from desktop button", async ({ page }) => {
      await pom.projectsPage.desktopLogoutButton.click();
      await expect(page).toHaveURL(/\/api\/auth\/signin/);
    });
  });

  test.describe("Tablet View (768px - 1279px)", () => {
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize({ width: 1024, height: 768 });
    });

    test("should display full navbar on tablet", async ({ page }) => {
      // Logo and brand should be visible
      const logo = page.getByTestId("tmeLogo");
      await expect(logo).toBeVisible();

      const brandText = page.locator("text=ProjectScout");
      await expect(brandText).toBeVisible();

      // Desktop navigation should still be visible on tablet
      const homeLink = page.locator("nav a[href='/']").first();
      const myProjectsLink = page.locator("nav a[href='/myProjects']").first();
      await expect(homeLink).toBeVisible();
      await expect(myProjectsLink).toBeVisible();

      // Desktop logout should be visible
      await expect(pom.projectsPage.desktopLogoutButton).toBeVisible();

      // Mobile menu button should be hidden
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await expect(mobileMenuButton).toBeHidden();
    });
  });

  test.describe("Mobile View (below 768px)", () => {
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
    });

    test("should display mobile navbar with hamburger menu", async ({ page }) => {
      // Logo and brand should be visible
      const logo = page.getByTestId("tmeLogo");
      await expect(logo).toBeVisible();

      const brandText = page.locator("text=ProjectScout");
      await expect(brandText).toBeVisible();

      // Desktop navigation links should be hidden
      const desktopNavLinks = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).first();
      await expect(desktopNavLinks).toBeHidden();

      // Desktop logout button should be hidden
      await expect(pom.projectsPage.desktopLogoutButton).toBeHidden();

      // Mobile menu button should be visible
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await expect(mobileMenuButton).toBeVisible();

      // Mobile menu should be hidden initially
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();
      await expect(mobileMenu).toBeHidden();
    });

    test("should toggle mobile menu when hamburger is clicked", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();

      // Initially closed
      await expect(mobileMenu).toBeHidden();

      // Click to open
      await mobileMenuButton.click();
      await expect(mobileMenu).toBeVisible();

      // Click to close
      await mobileMenuButton.click();
      await expect(mobileMenu).toBeHidden();
    });

    test("should display hamburger and close icons correctly", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      const hamburgerIcon = mobileMenuButton.locator("svg").first();
      const closeIcon = mobileMenuButton.locator("svg").nth(1);

      // Initially show hamburger, hide close
      await expect(hamburgerIcon).toBeVisible();
      await expect(closeIcon).toBeHidden();

      // After clicking, show close, hide hamburger
      await mobileMenuButton.click();
      await expect(hamburgerIcon).toBeHidden();
      await expect(closeIcon).toBeVisible();

      // After clicking again, back to hamburger
      await mobileMenuButton.click();
      await expect(hamburgerIcon).toBeVisible();
      await expect(closeIcon).toBeHidden();
    });

    test("should display mobile navigation links when menu is open", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      // Mobile navigation links should be visible - use more specific selectors
      const mobileHomeLink = page.locator("nav").getByRole("link", { name: "Home" }).last();
      const mobileMyProjectsLink = page.locator("nav").getByRole("link", { name: "My Projects" }).last();

      await expect(mobileHomeLink).toBeVisible();
      await expect(mobileMyProjectsLink).toBeVisible();
    });

    test("should highlight active link in mobile menu", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      // Home should be active on home page - use more specific selector
      const mobileHomeLink = page.locator("nav").getByRole("link", { name: "Home" }).last();
      await expect(mobileHomeLink).toHaveClass(/text-blue-600/);
      await expect(mobileHomeLink).toHaveClass(/bg-blue-50/);
    });

    test("should display user info and mobile logout in mobile menu", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      // Mobile logout button should be visible
      await expect(pom.projectsPage.mobileLogoutButton).toBeVisible();

      // User avatar and name should be visible in mobile menu - use more specific selector
      const userAvatar = page.locator("nav").locator("img[alt='User Avatar']").last();
      await expect(userAvatar).toBeVisible();
    });

    test("should close mobile menu when navigation link is clicked", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();

      // Open mobile menu
      await mobileMenuButton.click();
      await expect(mobileMenu).toBeVisible();

      // Click on My Projects link
      const mobileMyProjectsLink = page.locator("nav").getByRole("link", { name: "My Projects" }).last();
      await mobileMyProjectsLink.click();

      // Menu should close and navigate to My Projects
      await expect(mobileMenu).toBeHidden();
      await expect(page).toHaveURL("/myProjects");
    });

    test("should allow logout from mobile menu", async ({ page }) => {
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      await pom.projectsPage.mobileLogoutButton.click();
      await expect(page).toHaveURL(/\/api\/auth\/signin/);
    });
  });

  test.describe("Responsive Breakpoint Tests", () => {
    test("should transition from desktop to mobile layout", async ({ page }) => {
      // Start with desktop view
      await page.setViewportSize({ width: 1280, height: 720 });

      // Desktop elements should be visible
      await expect(pom.projectsPage.desktopLogoutButton).toBeVisible();
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await expect(mobileMenuButton).toBeHidden();

      // Resize to mobile
      await page.setViewportSize({ width: 375, height: 667 });

      // Mobile elements should now be visible
      await expect(pom.projectsPage.desktopLogoutButton).toBeHidden();
      await expect(mobileMenuButton).toBeVisible();
    });

    test("should maintain functionality across viewport changes", async ({ page }) => {
      // Start mobile, open menu
      await page.setViewportSize({ width: 375, height: 667 });
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();
      await expect(mobileMenu).toBeVisible();

      // Resize to desktop
      await page.setViewportSize({ width: 1280, height: 720 });

      // Desktop navigation should be visible, mobile menu should be hidden
      const desktopNavLinks = page.locator("nav").getByRole("link", { name: "Home" }).first();
      await expect(desktopNavLinks).toBeVisible();
      await expect(mobileMenu).toBeHidden();
    });
  });

  test.describe("Accessibility Tests", () => {
    test("should have proper ARIA attributes", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await expect(mobileMenuButton).toHaveAttribute("aria-expanded", "false");

      await mobileMenuButton.click();
      await expect(mobileMenuButton).toHaveAttribute("aria-expanded", "true");

      await mobileMenuButton.click();
      await expect(mobileMenuButton).toHaveAttribute("aria-expanded", "false");
    });

    test("should have screen reader text", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      const screenReaderText = page.locator(".sr-only");
      await expect(screenReaderText).toHaveText("Open main menu");
    });

    test("should be keyboard accessible", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");

      // Focus the button with Tab
      await page.keyboard.press("Tab");
      await expect(mobileMenuButton).toBeFocused();

      // Open menu with Enter
      await page.keyboard.press("Enter");
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();
      await expect(mobileMenu).toBeVisible();

      // Close menu with Escape (if implemented)
      await page.keyboard.press("Escape");
      // Note: This would require implementing Escape key handling in the component
    });
  });

  test.describe("Performance and Visual Tests", () => {
    test("should have smooth transitions", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();

      // Test multiple rapid toggles
      for (let i = 0; i < 3; i++) {
        await mobileMenuButton.click();
        await expect(mobileMenu).toBeVisible();
        await mobileMenuButton.click();
        await expect(mobileMenu).toBeHidden();
      }
    });

    test("should maintain layout integrity across different screen sizes", async ({ page }) => {
      const viewports = [
        { width: 320, height: 568 }, // iPhone SE
        { width: 375, height: 667 }, // iPhone 6/7/8
        { width: 414, height: 896 }, // iPhone XR
        { width: 768, height: 1024 }, // iPad
        { width: 1024, height: 768 }, // iPad Landscape
        { width: 1280, height: 720 }, // Desktop
        { width: 1920, height: 1080 }, // Large Desktop
      ];

      for (const viewport of viewports) {
        await page.setViewportSize(viewport);

        // Logo should always be visible
        const logo = page.getByTestId("tmeLogo");
        await expect(logo).toBeVisible();

        // Brand text should always be visible
        const brandText = page.locator("text=ProjectScout");
        await expect(brandText).toBeVisible();

        // Check appropriate navigation is visible based on viewport
        if (viewport.width >= 768) {
          // Desktop/tablet navigation should be visible
          const desktopNav = page.locator("nav a[href='/']").first();
          await expect(desktopNav).toBeVisible();
          await expect(pom.projectsPage.desktopLogoutButton).toBeVisible();
        } else {
          // Mobile menu button should be visible
          const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
          await expect(mobileMenuButton).toBeVisible();
        }
      }
    });
  });

  test.describe("Navigation Functionality", () => {
    test("should navigate correctly from mobile menu", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      // Start on home page
      await expect(page).toHaveURL("/");

      // Open mobile menu and navigate to My Projects
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      const mobileMyProjectsLink = page.locator("nav").getByRole("link", { name: "My Projects" }).last();
      await mobileMyProjectsLink.click();

      // Should navigate and close menu
      await expect(page).toHaveURL("/myProjects");
      const mobileMenu = page.locator("nav").locator("div").filter({ hasText: "Home" }).filter({ hasText: "My Projects" }).last();
      await expect(mobileMenu).toBeHidden();

      // Open menu again and navigate back to home
      await mobileMenuButton.click();
      const mobileHomeLink = page.locator("nav").getByRole("link", { name: "Home" }).last();
      await mobileHomeLink.click();

      await expect(page).toHaveURL("/");
      await expect(mobileMenu).toBeHidden();
    });

    test("should maintain active state consistency between desktop and mobile", async ({ page }) => {
      // Start on My Projects page
      await page.goto("/myProjects");

      // Desktop view - My Projects should be active
      await page.setViewportSize({ width: 1280, height: 720 });
      const desktopMyProjectsLink = page.locator("nav a[href='/myProjects']").first();
      await expect(desktopMyProjectsLink).toHaveClass(/text-blue-600/);

      // Switch to mobile view - My Projects should still be active in mobile menu
      await page.setViewportSize({ width: 375, height: 667 });
      const mobileMenuButton = page.locator("button[aria-label='Toggle navigation menu']");
      await mobileMenuButton.click();

      const mobileMyProjectsLink = page.locator("nav").getByRole("link", { name: "My Projects" }).last();
      await expect(mobileMyProjectsLink).toHaveClass(/text-blue-600/);
      await expect(mobileMyProjectsLink).toHaveClass(/bg-blue-50/);
    });
  });
});
