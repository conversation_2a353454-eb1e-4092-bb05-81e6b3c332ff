"use client";
import { useState } from "react";
import { useProjectContext } from "@/hooks/useProjectContext";
import { ProjectForm } from "./projectForm";
import { ConfirmDialog } from "../shared";
import { formatDate, deleteProject } from "@/utils";
import type { ProjectIdType, Project } from "@/types";
import { ProjectId, ProjectTitle, InfoItem, ActionGroup, DetailsContainer } from "./appliedProjectListItemComponents";

export const AppliedProjectListItem: React.FC<{
  project: Project;
  showToast: (message: string) => void;
}> = ({ project, showToast }) => {
  const { openNewProjectForm, endpoint } = useProjectContext();
  const [isEditing, setIsEditing] = useState(false);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<ProjectIdType | null>(null);

  const startEditing = () => {
    setIsEditing(true);
  };

  const onDuplicate = () => {
    openNewProjectForm(project);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const onDelete = (id: ProjectIdType) => {
    setProjectToDelete(id);
    setIsDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) return;

    try {
      await deleteProject(projectToDelete, endpoint);
      showToast("Project deleted successfully!");
    } catch (error) {
      console.error("Failed to delete project:", error);
      showToast("Failed to delete project.");
    } finally {
      setIsDialogOpen(false);
      setProjectToDelete(null);
    }
  };

  const projectStyles = {
    detailsWrapper: "flex flex-wrap justify-between mt-4 gap-4",
    detailsItem: "flex flex-col flex-1 gap-3 bg-gray-100 p-4 pt-0 rounded-lg",
  };

  return (
    <div
      style={{
        boxShadow: "1px 1px 2px 1px rgba(0,0,0,0.3), inset 1px 1px 2px 1px rgba(255, 255, 255)",
      }}
      className="px-4 py-4 mx-5 my-2 font-normal rounded-lg bg-gray-50"
      data-testid="projectItem"
    >
      {!isEditing ? (
        <>
          {/* title */}
          <div className="flex gap-4 items-start justify-between">
            <span className="flex gap-4">
              <ProjectId projectId={project.id} status={project.status || "active"} />
              <ProjectTitle projectLink={project.projectLink} projectTitle={project.projectTitle} refId={project.jobReference} />
            </span>

            <span className="flex gap-4 text-nowrap text-right">
              <InfoItem
                label="Date of Application"
                value={formatDate(project.dateOfApplication || new Date().toISOString(), "DDMMYYYY")}
                testId="dateOfApplication"
              />
            </span>
          </div>

          {/* details container */}
          <div className={projectStyles.detailsWrapper}>
            {/* project */}
            <DetailsContainer testId="projectDetails">
              <span className="flex mt-4">
                <h2 className="text-nowrap bold mr-1 text-blue-500">Project Details</h2>
                <hr className="border-blue-900 mb-2 mt-3 w-full" />
              </span>
              <InfoItem
                label="Project Start Date"
                value={project.dateOfProjectStart ? formatDate(project.dateOfProjectStart, "DDMMYYYY") : "---"}
                testId="dateOfProjectStart"
              />
              <InfoItem label="Role" value={project.role} testId="projectRole" />
              <InfoItem label="Location" value={project.location} testId="projectLocation" />
              <span className="flex gap-4">
                <InfoItem label="Remote" value={`${project.remote || "---"}%`} testId="remotePercentage" />
                <InfoItem label="Utilization" value={`${project.utilization || "---"}%`} testId="utilization" />
              </span>
            </DetailsContainer>

            {/* Offer */}
            <DetailsContainer testId="offerDetails">
              <span className="flex mt-4">
                <h2 className="text-nowrap bold mr-1 text-blue-500">Offer Details</h2>
                <hr className="border-blue-900 mb-2 mt-3 w-full" />
              </span>
              <InfoItem label="Created for" value={project.createdForUser.name} testId="createdForUser" />
              <InfoItem label="Created by" value={project.createdByUser.name} testId="createdByUser" />
              <InfoItem label="Platform" value={project.platform} testId="platform" />

              <span>
                <p>Net Rate:</p>
                <span className="flex gap-4">
                  <InfoItem label="Onsite" value={project.rateOnsite} testId="rateOnsite" />
                  <InfoItem label="Offsite" value={project.rateOffsite} testId="rateOffsite" />
                </span>
              </span>
            </DetailsContainer>

            {/* Contact */}
            <DetailsContainer testId="contactDetails">
              <span className="flex mt-4">
                <h2 className="text-nowrap bold mr-1 text-blue-500">Contact Details</h2>
                <hr className="border-blue-900 mb-2 mt-3 w-full" />
              </span>

              <InfoItem label="Agency" value={project.agency} testId="agency" />

              <div>
                <p className="text-sm text-gray-600">Point of Contact</p>
                <span className="flex">
                  <p className="text-md font-medium text-gray-900 leading-[.9] mr-1">
                    {!!project.contactFirstName && <span data-testid="contactFirstName">{project.contactFirstName}</span>}
                  </p>
                  <p className="text-md font-medium text-gray-900 leading-[.9]">
                    {!!project.contactLastName && <span data-testid="contactLastName">{project.contactLastName}</span>}
                  </p>

                  {!project.contactFirstName && !project.contactLastName && (
                    <span className="text-md font-medium text-gray-900 leading-[.9]">---</span>
                  )}
                </span>
              </div>

              <InfoItem label="Email" value={project.email} testId="contactEmail" />
              <InfoItem label="Phone" value={project.number} testId="contactNumber" />
            </DetailsContainer>

            {/* Comment */}
            <DetailsContainer testId="commentDetails">
              <span className="flex mt-4">
                <h2 className="text-nowrap bold mr-1 text-blue-500">Comment</h2>
                <hr className="border-blue-900 mb-2 mt-3 w-full" />
              </span>
              <p className="text-gray-900 min-h-[175px]">{project.comment ? <span data-testid="projectComment">{project.comment}</span> : "---"}</p>
            </DetailsContainer>
          </div>

          <ActionGroup
            projectId={project.id!!}
            startEditing={startEditing}
            onDuplicate={onDuplicate}
            onDelete={onDelete}
            lastUpdated={project.lastUpdated}
          />

          <ConfirmDialog
            isOpen={isDialogOpen}
            title="Are you sure you want to delete this project?"
            onConfirm={handleConfirmDelete}
            onCancel={() => {
              setIsDialogOpen(false);
              setProjectToDelete(null);
            }}
          />
        </>
      ) : (
        <ProjectForm project={project} closeProjectForm={() => setIsEditing(false)} isActive={isEditing} />
      )}
    </div>
  );
};
