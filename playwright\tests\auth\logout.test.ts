import { test, expect } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";

test("user can log out and is redirected to sign-in", async ({ page }) => {
  let pom: PomManager;
  pom = new PomManager(page);

  await pom.projectsPage.goToProjectsPage();

  await pom.projectsPage.desktopLogoutButton.click();

  await expect(page).toHaveURL(/\/api\/auth\/signin/);

  await expect(pom.projectsPage.desktopLogoutButton).toBeHidden();
});
