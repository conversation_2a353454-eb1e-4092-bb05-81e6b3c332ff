import { test, expect, type Locator } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";
import { getAllFormValueByTestId, isArraySorted } from "../../utils/testDataUtils";

test.describe("sort projects tests", () => {
  let pom: PomManager;
  let sortButtons: Record<
    string,
    {
      asc: Locator;
      desc: Locator;
    }
  >;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    sortButtons = pom.projectsPage.sortButtons;
    await pom.projectsPage.goToProjectsPage();
    const { sortIcon } = pom.projectsPage;
    await sortIcon.click();
  });

  const targets = [
    "projectId",
    "projectTitle",
    "agency",
    "contactFirstName",
    "rateOnsite",
    "dateOfApplication",
    "createdByUser",
    "createdForUser",
  ];

  targets.forEach((target) => {
    test(`should sort projects by ${target} ascending`, async () => {
      const { asc } = sortButtons[target];
      let values;

      await asc.click();
      values = (await getAllFormValueByTestId(pom, target)) as string[];
      expect(isArraySorted(values, "asc")).toBe(true);
      expect(isArraySorted(values, "desc")).toBe(false);
    });

    test(`should sort projects by ${target} descending`, async () => {
      const { desc } = sortButtons[target];
      let values;

      await desc.click();
      values = (await getAllFormValueByTestId(pom, target)) as string[];

      expect(isArraySorted(values, "desc")).toBe(true);
      expect(isArraySorted(values, "asc")).toBe(false);
    });
  });
});
